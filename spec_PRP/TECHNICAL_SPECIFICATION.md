# Technical Specification: scHybridNet

## Advanced Multi-Modal Graph Neural Network for Single-Cell Analysis

---

## Table of Contents

1. [System Architecture Overview](#1-system-architecture-overview)
2. [Data Flow and Processing Pipeline](#2-data-flow-and-processing-pipeline)
3. [Core Components Specification](#3-core-components-specification)
4. [Mathematical Formulations](#4-mathematical-formulations)
5. [Algorithm Implementations](#5-algorithm-implementations)
6. [API Specifications](#6-api-specifications)
7. [Performance Optimization](#7-performance-optimization)
8. [Testing Framework](#8-testing-framework)

---

## 1. System Architecture Overview

### 1.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                             scHybridNet Architecture                        │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  Input Layer                                                                │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐                   │
│  │Single-Cell  │  │Gene Embeddings│  │   PPI Network   │                   │
│  │Expression X │  │      E        │  │        P        │                   │
│  │  (C × G)    │  │   (G × d)     │  │   (G × G)       │                   │
│  └─────────────┘  └──────────────┘  └─────────────────┘                   │
│         │                │                     │                           │
│         └────────────────┼─────────────────────┘                           │
│                          │                                                 │
│  Graph Construction Layer                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────┤
│  │                                                                         │
│  │  ┌─────────────┐  ┌─────────────┐  ┌──────────────────┐                │
│  │  │Cell-Cell    │  │Gene-Gene    │  │  Cell-Gene       │                │
│  │  │Graph G_cc   │  │Graph G_gg   │  │ Bipartite G_cg   │                │
│  │  │(k-NN based) │  │(PPI + Coexp)│  │ (Expression)     │                │
│  │  └─────────────┘  └─────────────┘  └──────────────────┘                │
│  └─────────────────────────────────────────────────────────────────────────┤
│                          │                                                 │
│  Triple-Graph Encoder                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┤
│  │                                                                         │
│  │  ┌──────────────┐ ┌──────────────┐ ┌─────────────────┐                 │
│  │  │   Cell       │ │    Gene      │ │   Bipartite     │                 │
│  │  │ Transformer  │ │ Transformer  │ │   GCN           │                 │
│  │  │   Encoder    │ │  Encoder     │ │   Encoder       │                 │
│  │  └──────────────┘ └──────────────┘ └─────────────────┘                 │
│  │         │               │                  │                           │
│  │         └───────────────┼──────────────────┘                           │
│  │                         │                                              │
│  │            ┌─────────────────────────┐                                 │
│  │            │   Fusion Attention      │                                 │
│  │            │     Mechanism           │                                 │
│  │            └─────────────────────────┘                                 │
│  └─────────────────────────────────────────────────────────────────────────┤
│                          │                                                 │
│  Multi-Task Output Layer                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────┤
│  │                                                                         │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐    │
│  │ │Multi-Scale  │ │  Optimal    │ │Gene Expr.   │ │  Uncertainty    │    │
│  │ │CORN         │ │ Transport   │ │Recovery     │ │ Quantification  │    │
│  │ │Classifier   │ │ Alignment   │ │             │ │                 │    │
│  │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────────┘    │
│  └─────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  Output: Cell Classifications + Trajectories + Confidence Scores           │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 Module Dependencies

```
scHybridNet/
├── data/
│   ├── graph_builder.py          # Graph construction utilities
│   ├── preprocessor.py           # Data preprocessing pipeline
│   └── loaders.py                # Data loading and batching
├── models/
│   ├── core/
│   │   ├── triple_graph_encoder.py    # Main encoder architecture
│   │   ├── graph_transformers.py      # Individual graph processors
│   │   └── fusion_attention.py        # Multi-modal fusion layer
│   ├── classifiers/
│   │   ├── corn_classifier.py          # Multi-scale ordinal regression
│   │   └── uncertainty_module.py      # Bayesian uncertainty estimation
│   ├── losses/
│   │   ├── multi_task_loss.py         # Combined loss function
│   │   ├── optimal_transport.py       # Sinkhorn & Wasserstein losses
│   │   └── contrastive_loss.py        # Graph contrastive learning
│   └── utils/
│       ├── graph_ops.py               # Graph operations and sampling
│       ├── attention_utils.py         # Attention mechanisms
│       └── metrics.py                 # Evaluation metrics
└── training/
    ├── trainer.py                # Main training loop
    ├── scheduler.py              # Learning rate scheduling
    └── callbacks.py              # Training callbacks
```

---

## 2. Data Flow and Processing Pipeline

### 2.1 Input Processing Workflow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Raw scRNA-seq  │───▶│   Preprocessing   │───▶│   Graph         │
│     Data        │    │   & QC Filter     │    │ Construction    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ • Cell barcode  │    │ • Filter cells   │    │ • G_cc: k-NN    │
│ • Gene symbols  │    │ • Filter genes   │    │ • G_gg: PPI     │
│ • UMI counts    │    │ • Normalize      │    │ • G_cg: Bipart  │
│ • Metadata      │    │ • Log transform  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Feature        │
                       │  Engineering     │
                       └──────────────────┘
                                │
                                ▼
                    ┌─────────────────────────┐
                    │  Triple-Graph Encoder   │
                    └─────────────────────────┘
                                │
                                ▼
                    ┌─────────────────────────┐
                    │  Multi-Task Outputs     │
                    └─────────────────────────┘
```

### 2.2 Graph Construction Pipeline

#### 2.2.1 Cell-Cell Graph (G_cc)

```python
def build_cell_cell_graph(adata, n_neighbors=15, metric='euclidean'):
    """
    Build k-NN graph based on cell expression similarity
  
    Args:
        adata: AnnData object with normalized expression
        n_neighbors: Number of nearest neighbors
        metric: Distance metric
  
    Returns:
        sparse_matrix: Cell-cell adjacency matrix
    """
    # Compute PCA for dimensionality reduction
    sc.pp.pca(adata, n_comps=50)
  
    # Build k-NN graph
    sc.pp.neighbors(adata, n_neighbors=n_neighbors, 
                   n_pcs=50, metric=metric)
  
    # Extract adjacency matrix
    G_cc = adata.obsp['connectivities']
    return G_cc
```

#### 2.2.2 Gene-Gene Graph (G_gg)

```python
def build_gene_gene_graph(expression_matrix, ppi_network, 
                         threshold=0.5, coexp_weight=0.3):
    """
    Combine PPI network with co-expression relationships
  
    Args:
        expression_matrix: Gene expression data (cells × genes)
        ppi_network: Protein-protein interaction network
        threshold: PPI confidence threshold
        coexp_weight: Weight for co-expression edges
  
    Returns:
        sparse_matrix: Gene-gene adjacency matrix
    """
    # Filter PPI network by confidence
    ppi_filtered = ppi_network[ppi_network['confidence'] > threshold]
  
    # Compute gene co-expression
    gene_corr = np.corrcoef(expression_matrix.T)
  
    # Combine PPI and co-expression
    G_gg = combine_networks(ppi_filtered, gene_corr, coexp_weight)
    return G_gg
```

#### 2.2.3 Cell-Gene Bipartite Graph (G_cg)

```python
def build_bipartite_graph(expression_matrix, sparsity_threshold=0.1):
    """
    Create bipartite graph representing cell-gene relationships
  
    Args:
        expression_matrix: Expression data (cells × genes)
        sparsity_threshold: Minimum expression level
  
    Returns:
        sparse_matrix: Bipartite adjacency matrix
    """
    # Apply sparsity threshold
    expr_binary = (expression_matrix > sparsity_threshold).astype(int)
  
    # Create bipartite adjacency matrix
    n_cells, n_genes = expression_matrix.shape
    G_cg = scipy.sparse.lil_matrix((n_cells + n_genes, n_cells + n_genes))
  
    # Fill upper-right and lower-left blocks
    G_cg[:n_cells, n_cells:] = expr_binary
    G_cg[n_cells:, :n_cells] = expr_binary.T
  
    return G_cg.tocsr()
```

---

## 3. Core Components Specification

### 3.1 Triple-Graph Encoder

#### 3.1.1 Architecture Overview

```python
class TripleGraphEncoder(nn.Module):
    """
    Main encoder that processes three graph modalities simultaneously
    """
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int = 256,
                 num_layers: int = 4,
                 num_heads: int = 8,
                 dropout: float = 0.1):
        super().__init__()
      
        # Individual graph encoders
        self.cell_encoder = GraphTransformer(
            input_dim, hidden_dim, num_layers, num_heads, dropout
        )
        self.gene_encoder = GraphTransformer(
            input_dim, hidden_dim, num_layers, num_heads, dropout
        )
        self.bipartite_encoder = BipartiteGCN(
            input_dim, hidden_dim, num_layers, dropout
        )
      
        # Fusion mechanism
        self.fusion_attention = MultiModalFusion(
            hidden_dim, num_modalities=3, num_heads=num_heads
        )
      
        # Output projection
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
      
    def forward(self, features, G_cc, G_gg, G_cg):
        # Process each graph modality
        h_cell = self.cell_encoder(features, G_cc)
        h_gene = self.gene_encoder(features, G_gg) 
        h_bipartite = self.bipartite_encoder(features, G_cg)
      
        # Multi-modal fusion
        h_fused = self.fusion_attention(h_cell, h_gene, h_bipartite)
      
        # Output projection
        output = self.output_projection(h_fused)
      
        return output, {'cell': h_cell, 'gene': h_gene, 'bipartite': h_bipartite}
```

#### 3.1.2 Graph Transformer Layer

```python
class GraphTransformer(nn.Module):
    """
    Graph Transformer with attention mechanism for single graph processing
    """
    def __init__(self, input_dim, hidden_dim, num_layers, num_heads, dropout):
        super().__init__()
      
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        self.layers = nn.ModuleList([
            GraphTransformerLayer(hidden_dim, num_heads, dropout)
            for _ in range(num_layers)
        ])
        self.norm = nn.LayerNorm(hidden_dim)
      
    def forward(self, x, adj_matrix):
        h = self.input_projection(x)
      
        for layer in self.layers:
            h = layer(h, adj_matrix)
      
        h = self.norm(h)
        return h

class GraphTransformerLayer(nn.Module):
    """
    Individual transformer layer with graph attention
    """
    def __init__(self, hidden_dim, num_heads, dropout):
        super().__init__()
      
        self.attention = GraphMultiHeadAttention(hidden_dim, num_heads, dropout)
        self.feed_forward = FeedForward(hidden_dim, dropout)
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
      
    def forward(self, x, adj_matrix):
        # Self-attention with graph structure
        attn_output = self.attention(x, adj_matrix)
        x = self.norm1(x + attn_output)
      
        # Feed-forward network
        ff_output = self.feed_forward(x)
        x = self.norm2(x + ff_output)
      
        return x
```

### 3.2 Multi-Scale CORN Classifier

#### 3.2.1 Hierarchical Classification Architecture

```python
class MultiScaleCORNClassifier(nn.Module):
    """
    Hierarchical ordinal regression classifier for multi-scale cell typing
    """
    def __init__(self, 
                 input_dim: int,
                 hierarchy_config: Dict[str, int],
                 dropout: float = 0.1):
        super().__init__()
      
        self.hierarchy_config = hierarchy_config
        self.classifiers = nn.ModuleDict()
      
        for level, num_classes in hierarchy_config.items():
            self.classifiers[level] = CORNHead(
                input_dim, num_classes, dropout
            )
  
    def forward(self, embeddings):
        predictions = {}
      
        for level, classifier in self.classifiers.items():
            predictions[level] = classifier(embeddings)
          
        return predictions

class CORNHead(nn.Module):
    """
    CORN (Conditional Ordinal Regression for Neural networks) head
    """
    def __init__(self, input_dim, num_classes, dropout=0.1):
        super().__init__()
      
        self.num_classes = num_classes
        self.dropout = nn.Dropout(dropout)
      
        # Binary classifiers for each threshold
        self.binary_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, input_dim // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(input_dim // 2, 1),
                nn.Sigmoid()
            ) for _ in range(num_classes - 1)
        ])
  
    def forward(self, x):
        x = self.dropout(x)
      
        # Compute binary predictions for each threshold
        binary_preds = []
        for classifier in self.binary_classifiers:
            pred = classifier(x)
            binary_preds.append(pred)
      
        # Stack predictions
        return torch.cat(binary_preds, dim=-1)
```

### 3.3 Optimal Transport Module

#### 3.3.1 Dual Optimal Transport Implementation

```python
class DualOptimalTransport(nn.Module):
    """
    Implements bidirectional optimal transport for distribution alignment
    """
    def __init__(self, 
                 feature_dim: int,
                 eps: float = 0.1,
                 max_iter: int = 100,
                 reduction: str = 'mean'):
        super().__init__()
      
        self.sinkhorn = SinkhornDistance(eps, max_iter, reduction)
        self.wasserstein_fr = WassersteinFisherRao(eps, max_iter)
      
        # Learnable transport cost
        self.cost_network = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, 1),
            nn.Softplus()
        )
  
    def forward(self, cell_features, gene_features, cell_gene_matrix):
        # Forward transport: Cell → Gene
        forward_cost, forward_plan = self.compute_transport(
            cell_features, gene_features, direction='forward'
        )
      
        # Backward transport: Gene → Cell  
        backward_cost, backward_plan = self.compute_transport(
            gene_features, cell_features, direction='backward'
        )
      
        # Wasserstein-Fisher-Rao for dynamic trajectories
        wfr_cost = self.wasserstein_fr(cell_features, gene_features)
      
        return {
            'forward_cost': forward_cost,
            'backward_cost': backward_cost,
            'wfr_cost': wfr_cost,
            'forward_plan': forward_plan,
            'backward_plan': backward_plan
        }
  
    def compute_transport(self, source, target, direction='forward'):
        # Compute pairwise cost matrix
        cost_matrix = self.compute_cost_matrix(source, target)
      
        # Sinkhorn algorithm for optimal transport
        cost, plan, _ = self.sinkhorn(source, target, cost_matrix)
      
        return cost, plan
  
    def compute_cost_matrix(self, x, y):
        # Expand dimensions for pairwise computation
        x_expanded = x.unsqueeze(1)  # (batch, 1, dim)
        y_expanded = y.unsqueeze(0)  # (1, batch, dim)
      
        # Concatenate for cost network
        pairs = torch.cat([
            x_expanded.expand(-1, y.size(0), -1),
            y_expanded.expand(x.size(0), -1, -1)
        ], dim=-1)
      
        # Compute learned cost
        cost = self.cost_network(pairs).squeeze(-1)
      
        return cost
```

### 3.4 Uncertainty Quantification Module

#### 3.4.1 Bayesian Framework Implementation

```python
class BayesianUncertaintyModule(nn.Module):
    """
    Implements Monte Carlo Dropout and variational inference for uncertainty
    """
    def __init__(self, 
                 input_dim: int,
                 num_samples: int = 100,
                 dropout_rate: float = 0.1):
        super().__init__()
      
        self.num_samples = num_samples
        self.dropout_rate = dropout_rate
      
        # Variational layers
        self.mean_layer = nn.Linear(input_dim, input_dim)
        self.logvar_layer = nn.Linear(input_dim, input_dim)
      
        # Prediction head with dropout
        self.prediction_head = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(input_dim // 2, 1)
        )
  
    def forward(self, x, return_uncertainty=False):
        if not return_uncertainty:
            return self.prediction_head(x)
      
        # Monte Carlo sampling
        predictions = []
        self.train()  # Enable dropout during inference
      
        for _ in range(self.num_samples):
            pred = self.prediction_head(x)
            predictions.append(pred)
      
        predictions = torch.stack(predictions, dim=0)
      
        # Compute statistics
        mean_pred = predictions.mean(dim=0)
        var_pred = predictions.var(dim=0)
      
        return mean_pred, var_pred
  
    def kl_divergence(self, x):
        """Compute KL divergence for variational inference"""
        mean = self.mean_layer(x)
        logvar = self.logvar_layer(x)
      
        kl_div = -0.5 * torch.sum(1 + logvar - mean**2 - torch.exp(logvar), dim=-1)
        return kl_div.mean()
```

---

## 4. Mathematical Formulations

### 4.1 Graph Construction Formulas

#### 4.1.1 Cell-Cell Graph Construction

```
G_cc[i,j] = exp(-||x_i - x_j||²/σ²) if j ∈ kNN(i), 0 otherwise

Where:
- x_i, x_j: PCA-reduced cell representations
- σ: bandwidth parameter
- kNN(i): k-nearest neighbors of cell i
```

#### 4.1.2 Gene-Gene Graph Construction

```
G_gg = α·P + (1-α)·C

Where:
- P: PPI network adjacency matrix
- C: Gene co-expression matrix
- α: Balance parameter (typically 0.7)

C[i,j] = max(0, corr(g_i, g_j)) if |corr(g_i, g_j)| > threshold
```

### 4.2 Triple-Graph Processing

#### 4.2.1 Graph Convolution Operation

For each graph G ∈ {G_cc, G_gg, G_cg}:

```
H^(l+1) = σ(D^(-1/2) A D^(-1/2) H^(l) W^(l))

Where:
- A: Adjacency matrix of graph G
- D: Degree matrix (D_ii = Σ_j A_ij)
- H^(l): Node representations at layer l
- W^(l): Learnable weight matrix
- σ: Activation function (GELU)
```

#### 4.2.2 Multi-Modal Attention Fusion

```
Attention(Q,K,V) = softmax(QK^T/√d_k)V

For multi-modal fusion:
Q_fused = Concat(Q_cell, Q_gene, Q_bipartite)
K_fused = Concat(K_cell, K_gene, K_bipartite)  
V_fused = Concat(V_cell, V_gene, V_bipartite)

H_fused = Attention(Q_fused, K_fused, V_fused)
```

### 4.3 Multi-Scale CORN Loss

#### 4.3.1 CORN Loss Formulation

```
For ordinal regression with K classes:

L_CORN = Σ_{k=1}^{K-1} Σ_{i∈S_k} CE(f_k(x_i), y_i^k)

Where:
- S_k = {i | y_i ≥ k-1}: Training examples for threshold k
- y_i^k = 1 if y_i > k, 0 otherwise
- CE: Binary cross-entropy loss
- f_k: Binary classifier for threshold k

For multi-scale classification:
L_total = Σ_s λ_s · L_CORN^s

Where s indexes different scales (coarse, fine, dynamic)
```

### 4.4 Optimal Transport Formulation

#### 4.4.1 Sinkhorn Distance

```
W_ε(μ,ν) = min_{π∈Π(μ,ν)} <C,π> + ε·H(π)

Where:
- μ, ν: Source and target distributions
- C: Cost matrix
- π: Transport plan
- H(π): Entropy regularization
- ε: Regularization parameter

Sinkhorn iterations:
u^{(k+1)} = μ ⊘ (K v^{(k)})
v^{(k+1)} = ν ⊘ (K^T u^{(k+1)})

Where K = exp(-C/ε) and ⊘ denotes element-wise division
```

#### 4.4.2 Wasserstein-Fisher-Rao Distance

```
WFR_ε(μ,ν) = min_{π,g,h} ∫ c(x,y) dπ(x,y) + ε·H(π) + λ∫|∇g|²dμ + λ∫|∇h|²dν

Where:
- g, h: Potential functions
- λ: Regularization parameter for growth/death terms
- Handles unbalanced transport for dynamic trajectories
```

---

## 5. Algorithm Implementations

### 5.1 Training Algorithm

#### 5.1.1 Main Training Loop

```python
def train_epoch(model, dataloader, optimizer, loss_fn, device):
    """
    Single training epoch for scHybridNet
    """
    model.train()
    total_loss = 0.0
    metrics = {'corn_loss': 0, 'ot_loss': 0, 'recon_loss': 0}
  
    for batch_idx, batch in enumerate(dataloader):
        # Move data to device
        features = batch['features'].to(device)
        G_cc = batch['cell_graph'].to(device)
        G_gg = batch['gene_graph'].to(device)
        G_cg = batch['bipartite_graph'].to(device)
        labels = batch['labels'].to(device)
      
        # Forward pass
        outputs = model(features, G_cc, G_gg, G_cg)
      
        # Compute multi-task loss
        loss_dict = loss_fn(outputs, labels, features)
        total_batch_loss = loss_dict['total_loss']
      
        # Backward pass
        optimizer.zero_grad()
        total_batch_loss.backward()
      
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
      
        optimizer.step()
      
        # Update metrics
        total_loss += total_batch_loss.item()
        for key in metrics:
            if key in loss_dict:
                metrics[key] += loss_dict[key].item()
      
        # Dynamic graph sampling for large datasets
        if batch_idx % 100 == 0:
            model.update_graph_sampling_strategy()
  
    # Average metrics over epoch
    avg_metrics = {k: v / len(dataloader) for k, v in metrics.items()}
    avg_metrics['total_loss'] = total_loss / len(dataloader)
  
    return avg_metrics
```

#### 5.1.2 Dynamic Graph Sampling

```python
def dynamic_graph_sampling(graphs, node_features, batch_size=1000, k_hop=2):
    """
    Hierarchical sampling for scalable training on large graphs
    """
    n_nodes = node_features.size(0)
  
    if n_nodes <= batch_size:
        return graphs, node_features, torch.arange(n_nodes)
  
    # Sample seed nodes
    seed_indices = torch.randperm(n_nodes)[:batch_size//4]
  
    # Expand to k-hop neighborhood
    sampled_nodes = set(seed_indices.tolist())
  
    for graph in graphs.values():
        for hop in range(k_hop):
            new_neighbors = set()
            for node in sampled_nodes:
                neighbors = graph[node].nonzero().flatten()
                new_neighbors.update(neighbors.tolist())
            sampled_nodes.update(new_neighbors)
          
            if len(sampled_nodes) >= batch_size:
                break
  
    # Convert to tensor
    sampled_indices = torch.tensor(list(sampled_nodes)[:batch_size])
  
    # Extract subgraphs
    subgraphs = {}
    for name, graph in graphs.items():
        subgraphs[name] = graph[sampled_indices][:, sampled_indices]
  
    sub_features = node_features[sampled_indices]
  
    return subgraphs, sub_features, sampled_indices
```

### 5.2 Inference Algorithm

#### 5.2.1 Prediction with Uncertainty

```python
def predict_with_uncertainty(model, features, graphs, num_samples=100):
    """
    Generate predictions with uncertainty quantification
    """
    model.eval()
    predictions = []
    uncertainties = []
  
    # Monte Carlo sampling
    for _ in range(num_samples):
        model.train()  # Enable dropout
      
        with torch.no_grad():
            outputs = model(features, graphs['G_cc'], graphs['G_gg'], graphs['G_cg'])
            predictions.append(outputs['predictions'])
  
    # Stack predictions
    all_preds = torch.stack(predictions, dim=0)  # (num_samples, batch, classes)
  
    # Compute statistics
    mean_pred = all_preds.mean(dim=0)
    var_pred = all_preds.var(dim=0)
    epistemic_uncertainty = var_pred
  
    # Aleatoric uncertainty from model
    model.eval()
    with torch.no_grad():
        final_outputs = model(features, graphs['G_cc'], graphs['G_gg'], graphs['G_cg'])
        aleatoric_uncertainty = final_outputs.get('uncertainty', torch.zeros_like(var_pred))
  
    # Total uncertainty
    total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty
  
    return {
        'predictions': mean_pred,
        'epistemic_uncertainty': epistemic_uncertainty,
        'aleatoric_uncertainty': aleatoric_uncertainty,
        'total_uncertainty': total_uncertainty
    }
```

### 5.3 Graph Construction Algorithm

#### 5.3.1 Adaptive Graph Construction

```python
def adaptive_graph_construction(expression_matrix, ppi_network, 
                               dynamic_threshold=True, confidence_weights=True):
    """
    Adaptively construct graphs based on data characteristics
    """
    n_cells, n_genes = expression_matrix.shape
  
    # Cell-cell graph with adaptive k
    k_cc = min(15, max(5, int(np.sqrt(n_cells))))  # Adaptive k based on dataset size
    G_cc = build_adaptive_knn_graph(expression_matrix, k_cc)
  
    # Gene-gene graph with confidence weighting
    if confidence_weights:
        ppi_weights = compute_confidence_weights(ppi_network, expression_matrix)
        ppi_network['weight'] = ppi_weights
  
    G_gg = build_weighted_gene_graph(expression_matrix, ppi_network, 
                                    dynamic_threshold)
  
    # Bipartite graph with sparsity adaptation
    sparsity_level = compute_sparsity_level(expression_matrix)
    G_cg = build_adaptive_bipartite_graph(expression_matrix, sparsity_level)
  
    return {'G_cc': G_cc, 'G_gg': G_gg, 'G_cg': G_cg}

def compute_confidence_weights(ppi_network, expression_matrix):
    """
    Compute confidence weights for PPI edges based on expression correlation
    """
    gene_names = list(expression_matrix.columns)
    weights = []
  
    for _, edge in ppi_network.iterrows():
        gene1, gene2 = edge['gene1'], edge['gene2']
      
        if gene1 in gene_names and gene2 in gene_names:
            idx1 = gene_names.index(gene1)
            idx2 = gene_names.index(gene2)
          
            # Correlation between genes
            corr = np.corrcoef(expression_matrix.iloc[:, idx1], 
                              expression_matrix.iloc[:, idx2])[0, 1]
          
            # Combine PPI confidence with expression correlation
            ppi_conf = edge.get('confidence', 0.5)
            combined_weight = 0.7 * ppi_conf + 0.3 * abs(corr)
        else:
            combined_weight = edge.get('confidence', 0.5)
      
        weights.append(combined_weight)
  
    return weights
```

---

## 6. API Specifications

### 6.1 Main Model API

#### 6.1.1 scHybridNet Class Interface

```python
class scHybridNet:
    """
    Main interface for the scHybridNet model
    """
  
    def __init__(self,
                 n_genes: int,
                 n_cell_types: Dict[str, int],
                 hidden_dim: int = 256,
                 num_layers: int = 4,
                 dropout: float = 0.1,
                 device: str = 'cuda'):
        """
        Initialize scHybridNet model
      
        Parameters:
        -----------
        n_genes : int
            Number of genes in the dataset
        n_cell_types : Dict[str, int]
            Hierarchy of cell types {'level_name': num_classes}
        hidden_dim : int
            Hidden dimension size
        num_layers : int
            Number of transformer layers
        dropout : float
            Dropout rate
        device : str
            Computing device ('cuda' or 'cpu')
        """
  
    def fit(self,
            adata: anndata.AnnData,
            ppi_network: pd.DataFrame = None,
            batch_size: int = 512,
            epochs: int = 100,
            learning_rate: float = 1e-4,
            validation_split: float = 0.2,
            early_stopping: bool = True,
            verbose: bool = True) -> Dict:
        """
        Train the scHybridNet model
      
        Parameters:
        -----------
        adata : anndata.AnnData
            Single-cell expression data with cell type labels
        ppi_network : pd.DataFrame
            Protein-protein interaction network
        batch_size : int
            Training batch size
        epochs : int
            Number of training epochs
        learning_rate : float
            Learning rate
        validation_split : float
            Fraction of data for validation
        early_stopping : bool
            Whether to use early stopping
        verbose : bool
            Whether to print training progress
          
        Returns:
        --------
        Dict
            Training history and final metrics
        """
  
    def predict(self,
                adata: anndata.AnnData,
                return_uncertainty: bool = True,
                return_embeddings: bool = False,
                batch_size: int = 1000) -> Dict:
        """
        Make predictions on new data
      
        Parameters:
        -----------
        adata : anndata.AnnData
            Single-cell expression data
        return_uncertainty : bool
            Whether to return uncertainty estimates
        return_embeddings : bool
            Whether to return learned embeddings
        batch_size : int
            Batch size for inference
          
        Returns:
        --------
        Dict
            Predictions, uncertainties, and optional embeddings
        """
  
    def predict_trajectory(self,
                          adata: anndata.AnnData,
                          start_cells: List[str] = None,
                          end_cells: List[str] = None) -> Dict:
        """
        Infer developmental trajectories
      
        Parameters:
        -----------
        adata : anndata.AnnData
            Single-cell expression data
        start_cells : List[str]
            Starting cell types for trajectory
        end_cells : List[str]
            Ending cell types for trajectory
          
        Returns:
        --------
        Dict
            Trajectory information and pseudotime
        """
  
    def save_model(self, filepath: str) -> None:
        """Save trained model to disk"""
  
    def load_model(self, filepath: str) -> None:
        """Load trained model from disk"""
```

#### 6.1.2 Preprocessing API

```python
class scHybridNetPreprocessor:
    """
    Data preprocessing utilities for scHybridNet
    """
  
    @staticmethod
    def preprocess_adata(adata: anndata.AnnData,
                        min_genes: int = 200,
                        min_cells: int = 3,
                        target_sum: float = 1e4,
                        n_top_genes: int = 3000) -> anndata.AnnData:
        """
        Standard preprocessing pipeline for single-cell data
        """
  
    @staticmethod
    def load_ppi_network(filepath: str,
                        confidence_threshold: float = 0.5) -> pd.DataFrame:
        """
        Load and filter protein-protein interaction network
        """
  
    @staticmethod
    def build_graphs(adata: anndata.AnnData,
                    ppi_network: pd.DataFrame = None) -> Dict:
        """
        Construct triple graphs from preprocessed data
        """
```

### 6.2 Utility Functions API

#### 6.2.1 Evaluation Utilities

```python
def evaluate_classification(y_true: np.ndarray,
                           y_pred: np.ndarray,
                           uncertainty: np.ndarray = None) -> Dict:
    """
    Comprehensive evaluation of classification performance
  
    Returns:
    --------
    Dict containing:
    - accuracy, f1_score, balanced_accuracy
    - confusion_matrix
    - per_class_metrics
    - uncertainty_calibration (if uncertainty provided)
    """

def evaluate_trajectory_inference(adata: anndata.AnnData,
                                 pseudotime_pred: np.ndarray,
                                 ground_truth_time: np.ndarray = None) -> Dict:
    """
    Evaluate trajectory inference performance
  
    Returns:
    --------
    Dict containing:
    - kendall_tau_correlation
    - trajectory_conservation_score
    - branch_assignment_accuracy
    """

def plot_results(adata: anndata.AnnData,
                predictions: Dict,
                output_dir: str = './results/') -> None:
    """
    Generate comprehensive visualization of results
  
    Creates:
    - UMAP plots with predictions and uncertainty
    - Confusion matrices
    - Trajectory plots
    - Attention weight visualizations
    """
```

---

## 7. Performance Optimization

### 7.1 Memory Optimization

#### 7.1.1 Gradient Checkpointing

```python
class MemoryEfficientTripleEncoder(nn.Module):
    """
    Memory-efficient version using gradient checkpointing
    """
    def __init__(self, *args, **kwargs):
        super().__init__()
        # ... initialization
        self.use_checkpoint = True
  
    def forward(self, x, graphs):
        if self.use_checkpoint and self.training:
            return checkpoint(self._forward_impl, x, graphs)
        else:
            return self._forward_impl(x, graphs)
  
    def _forward_impl(self, x, graphs):
        # Actual forward computation
        pass
```

#### 7.1.2 Dynamic Graph Sparsification

```python
def dynamic_sparsification(adj_matrix, attention_weights, 
                          sparsity_ratio=0.1, min_edges=1000):
    """
    Dynamically sparsify graphs during training based on attention
    """
    # Compute edge importance scores
    edge_scores = attention_weights * adj_matrix
  
    # Determine sparsification threshold
    n_edges_to_keep = max(min_edges, 
                         int(adj_matrix.nnz * (1 - sparsity_ratio)))
  
    # Keep top-k edges
    threshold = torch.topk(edge_scores.flatten(), 
                          n_edges_to_keep).values[-1]
  
    sparsified_adj = adj_matrix * (edge_scores >= threshold).float()
  
    return sparsified_adj
```

### 7.2 Computational Optimization

#### 7.2.1 Mixed Precision Training

```python
def train_with_mixed_precision(model, dataloader, optimizer, loss_fn):
    """
    Training loop with automatic mixed precision
    """
    scaler = torch.cuda.amp.GradScaler()
  
    for batch in dataloader:
        optimizer.zero_grad()
      
        with torch.cuda.amp.autocast():
            outputs = model(batch['features'], batch['graphs'])
            loss = loss_fn(outputs, batch['labels'])
      
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
```

#### 7.2.2 Multi-GPU Training

```python
class DistributedTraining:
    """
    Distributed training utilities for scHybridNet
    """
  
    @staticmethod
    def setup_distributed(rank, world_size):
        """Initialize distributed training"""
        os.environ['MASTER_ADDR'] = 'localhost'
        os.environ['MASTER_PORT'] = '12355'
        dist.init_process_group("nccl", rank=rank, world_size=world_size)
  
    @staticmethod
    def prepare_model(model, device):
        """Wrap model for distributed training"""
        model = model.to(device)
        model = DDP(model, device_ids=[device])
        return model
  
    @staticmethod
    def prepare_dataloader(dataset, batch_size, rank, world_size):
        """Prepare distributed dataloader"""
        sampler = DistributedSampler(dataset, 
                                   num_replicas=world_size, 
                                   rank=rank)
        dataloader = DataLoader(dataset, 
                               batch_size=batch_size, 
                               sampler=sampler)
        return dataloader
```

---

## 8. Testing Framework

### 8.1 Unit Tests

#### 8.1.1 Model Component Tests

```python
import pytest
import torch
from models.core.triple_graph_encoder import TripleGraphEncoder

class TestTripleGraphEncoder:
    """Test suite for TripleGraphEncoder"""
  
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        batch_size, n_nodes, input_dim = 32, 1000, 100
        features = torch.randn(batch_size, n_nodes, input_dim)
      
        # Mock adjacency matrices
        G_cc = torch.rand(n_nodes, n_nodes) > 0.9
        G_gg = torch.rand(n_nodes, n_nodes) > 0.95
        G_cg = torch.rand(2*n_nodes, 2*n_nodes) > 0.98
      
        return features, {'G_cc': G_cc, 'G_gg': G_gg, 'G_cg': G_cg}
  
    def test_forward_pass(self, setup_data):
        """Test forward pass functionality"""
        features, graphs = setup_data
      
        encoder = TripleGraphEncoder(
            input_dim=100, hidden_dim=64, num_layers=2
        )
      
        output, intermediates = encoder(features, **graphs)
      
        # Check output shapes
        assert output.shape == (32, 1000, 64)
        assert len(intermediates) == 3
        assert all(k in intermediates for k in ['cell', 'gene', 'bipartite'])
  
    def test_gradient_flow(self, setup_data):
        """Test gradient computation"""
        features, graphs = setup_data
      
        encoder = TripleGraphEncoder(
            input_dim=100, hidden_dim=64, num_layers=2
        )
      
        output, _ = encoder(features, **graphs)
        loss = output.sum()
        loss.backward()
      
        # Check gradients exist
        for param in encoder.parameters():
            assert param.grad is not None
```

### 8.2 Integration Tests

#### 8.2.1 End-to-End Training Test

```python
class TestEndToEndTraining:
    """Integration tests for complete training pipeline"""
  
    def test_training_loop(self):
        """Test complete training pipeline"""
        # Generate synthetic data
        adata = generate_synthetic_data(n_cells=1000, n_genes=500)
      
        # Initialize model
        model = scHybridNet(
            n_genes=500,
            n_cell_types={'coarse': 5, 'fine': 20},
            hidden_dim=64,
            num_layers=2
        )
      
        # Train for few epochs
        history = model.fit(adata, epochs=2, batch_size=64, verbose=False)
      
        # Check training completed
        assert 'loss' in history
        assert len(history['loss']) == 2
      
        # Test prediction
        predictions = model.predict(adata, return_uncertainty=True)
      
        # Check prediction format
        assert 'predictions' in predictions
        assert 'uncertainty' in predictions
        assert predictions['predictions'].shape[0] == 1000

def generate_synthetic_data(n_cells=1000, n_genes=500, n_cell_types=5):
    """Generate synthetic single-cell data for testing"""
    # Simulate expression data
    expression = np.random.negative_binomial(5, 0.3, size=(n_cells, n_genes))
  
    # Create AnnData object
    adata = anndata.AnnData(X=expression.astype(np.float32))
  
    # Add synthetic cell type labels
    cell_types = np.random.randint(0, n_cell_types, n_cells)
    adata.obs['cell_type'] = [f'Type_{i}' for i in cell_types]
  
    # Add gene names
    adata.var['gene_names'] = [f'Gene_{i}' for i in range(n_genes)]
  
    return adata
```

### 8.3 Performance Tests

#### 8.3.1 Scalability Benchmarks

```python
class TestScalability:
    """Performance and scalability tests"""
  
    @pytest.mark.parametrize("n_cells,n_genes", [
        (1000, 500),
        (5000, 1000),
        (10000, 2000),
        (50000, 3000)
    ])
    def test_scalability(self, n_cells, n_genes):
        """Test model scalability with different data sizes"""
        import time
      
        # Generate data
        adata = generate_synthetic_data(n_cells, n_genes)
      
        # Initialize model
        model = scHybridNet(n_genes=n_genes, 
                           n_cell_types={'coarse': 5},
                           hidden_dim=64)
      
        # Measure training time
        start_time = time.time()
        model.fit(adata, epochs=1, verbose=False)
        training_time = time.time() - start_time
      
        # Measure prediction time
        start_time = time.time()
        predictions = model.predict(adata)
        prediction_time = time.time() - start_time
      
        # Log performance metrics
        print(f"Data: {n_cells} cells × {n_genes} genes")
        print(f"Training time: {training_time:.2f}s")
        print(f"Prediction time: {prediction_time:.2f}s")
      
        # Assert reasonable performance
        assert training_time < 300  # < 5 minutes
        assert prediction_time < 60  # < 1 minute
  
    def test_memory_usage(self):
        """Test memory usage during training"""
        import psutil
        import os
      
        process = psutil.Process(os.getpid())
      
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
      
        # Large dataset
        adata = generate_synthetic_data(n_cells=20000, n_genes=3000)
      
        model = scHybridNet(n_genes=3000, 
                           n_cell_types={'coarse': 10},
                           hidden_dim=128)
      
        # Training memory
        model.fit(adata, epochs=1, batch_size=256, verbose=False)
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
      
        memory_increase = peak_memory - baseline_memory
      
        print(f"Memory increase during training: {memory_increase:.1f} MB")
      
        # Assert reasonable memory usage (< 8GB increase)
        assert memory_increase < 8000
```

---

*This technical specification provides the complete implementation details for the scHybridNet architecture, covering all major components, algorithms, and optimization strategies. The modular design ensures maintainability and extensibility for future enhancements.*
