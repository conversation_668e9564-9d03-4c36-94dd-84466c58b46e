# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=conda_forge
_openmp_mutex=4.5=2_kmp_llvm
absl-py=1.4.0=pyhd8ed1ab_0
anndata=0.10.2=pypi_0
annotated-types=0.5.0=pyhd8ed1ab_0
anyio=3.7.1=pyhd8ed1ab_0
appdirs=1.4.4=pypi_0
array-api-compat=1.4=pypi_0
arrow=1.2.3=pyhd8ed1ab_0
asttokens=2.0.5=pyhd3eb1b0_0
attrs=23.1.0=pyh71513ae_1
backcall=0.2.0=pyhd3eb1b0_0
backports=1.0=pyhd8ed1ab_3
backports.functools_lru_cache=1.6.5=pyhd8ed1ab_0
beautifulsoup4=4.12.2=pyha770c72_0
bidcell=1.0.3=pypi_0
blas=1.0=mkl
blessed=1.19.1=pyhe4f9e05_2
brotli=1.0.9=h166bdaf_9
brotli-bin=1.0.9=h166bdaf_9
brotlipy=0.7.0=py39hb9d737c_1005
bzip2=1.0.8=h7f98852_4
c-ares=1.19.1=hd590300_0
ca-certificates=2023.05.30=h06a4308_0
cachecontrol=0.12.11=py39h06a4308_1
cached-property=1.5.2=hd8ed1ab_1
cached_property=1.5.2=pyha770c72_1
cellpose=2.2.3=pypi_0
certifi=2023.5.7=py39h06a4308_0
cffi=1.15.1=py39h5eee18b_3
charset-normalizer=3.2.0=pyhd8ed1ab_0
chex=0.1.81=pyhd8ed1ab_1
cleo=2.0.1=pyhd8ed1ab_0
click=8.1.4=unix_pyh707e725_0
colorama=0.4.6=pyhd8ed1ab_0
comm=0.1.2=py39h06a4308_0
contextlib2=21.6.0=pyhd8ed1ab_0
contourpy=1.1.0=py39h7633fee_0
crashtest=0.4.1=pyhd8ed1ab_0
croniter=1.3.15=pyhd8ed1ab_0
cryptography=41.0.2=py39hd4f0224_0
cuda-cudart=11.8.89=0
cuda-cupti=11.8.87=0
cuda-libraries=11.8.0=0
cuda-nvrtc=11.8.89=0
cuda-nvtx=11.8.86=0
cuda-runtime=11.8.0=0
cycler=0.11.0=pyhd8ed1ab_0
dateutils=0.6.12=py_0
dbus=1.13.6=h5008d03_3
debugpy=1.5.1=py39h295c915_0
decorator=5.1.1=pyhd3eb1b0_0
deepdiff=6.3.1=pyhd8ed1ab_0
distlib=0.3.6=pyhd8ed1ab_0
dm-tree=0.1.7=py39h1832856_0
docker-pycreds=0.4.0=pypi_0
docrep=0.3.2=pyh44b312d_0
dulwich=0.21.5=py39hd1e30aa_0
efficientnet-pytorch=0.7.1=pypi_0
et_xmlfile=1.1.0=pyhd8ed1ab_0
exceptiongroup=1.1.2=pyhd8ed1ab_0
executing=0.8.3=pyhd3eb1b0_0
expat=2.5.0=hcb278e6_1
fastapi=0.100.0=pyhd8ed1ab_0
fastremap=1.14.1=pypi_0
ffmpeg=4.3=hf484d3e_0
filelock=3.9.0=py39h06a4308_0
flax=0.6.1=pyhd8ed1ab_1
fonttools=4.41.0=py39hd1e30aa_0
freetype=2.12.1=hca18f0e_1
fsspec=2023.6.0=pyh1a96a4e_0
gettext=0.21.1=h27087fc_0
gitdb=4.0.10=pypi_0
gitpython=3.1.32=pypi_0
gmp=6.2.1=h295c915_3
gmpy2=2.1.2=py39heeb90bb_0
gnutls=3.6.15=he1e5248_0
h11=0.14.0=pyhd8ed1ab_0
h5py=3.9.0=nompi_py39h680ca82_101
hdf5=1.14.1=nompi_h4f84152_100
html5lib=1.1=pyh9f0ad1d_0
huggingface-hub=0.20.2=pypi_0
idna=3.4=pyhd8ed1ab_0
imagecodecs=2024.1.1=pypi_0
imageio=2.33.1=pypi_0
imgaug=0.4.0=pypi_0
importlib-metadata=6.8.0=pyha770c72_0
importlib-resources=6.0.0=pyhd8ed1ab_1
importlib_metadata=6.8.0=hd8ed1ab_0
importlib_resources=6.0.0=pyhd8ed1ab_1
inquirer=3.1.3=pyhd8ed1ab_0
intel-openmp=2023.1.0=hdb19cb5_46305
ipykernel=6.19.2=py39hb070fc8_0
ipython=8.12.0=py39h06a4308_0
itsdangerous=2.1.2=pyhd8ed1ab_0
jaraco.classes=3.3.0=pyhd8ed1ab_0
jax=0.4.13=pyhd8ed1ab_0
jaxlib=0.4.12=cpu_py39h4646849_1
jedi=0.18.1=py39h06a4308_1
jeepney=0.8.0=pyhd8ed1ab_0
jinja2=3.1.2=py39h06a4308_0
joblib=1.3.0=pyhd8ed1ab_1
jpeg=9e=h5eee18b_1
jsonschema=4.18.1=pyhd8ed1ab_0
jsonschema-specifications=2023.6.1=pyhd8ed1ab_0
jupyter_client=8.1.0=py39h06a4308_0
jupyter_core=5.3.0=py39h06a4308_0
keyring=23.13.1=py39hf3d152e_0
keyutils=1.6.1=h166bdaf_0
kiwisolver=1.4.4=py39hf939315_1
krb5=1.20.1=h81ceb04_0
lame=3.100=h7b6447c_0
lazy-loader=0.3=pypi_0
lcms2=2.12=h3be6417_0
ld_impl_linux-64=2.38=h1181459_1
lerc=4.0.0=h27087fc_0
libabseil=20230125.3=cxx17_h59595ed_0
libaec=1.0.6=hcb278e6_1
libblas=3.9.0=16_linux64_mkl
libbrotlicommon=1.0.9=h166bdaf_9
libbrotlidec=1.0.9=h166bdaf_9
libbrotlienc=1.0.9=h166bdaf_9
libcblas=3.9.0=16_linux64_mkl
libcublas=*********=0
libcufft=*********=0
libcufile=*********=0
libcurand=*********=0
libcurl=8.1.2=h409715c_0
libcusolver=11.4.1.48=0
libcusparse=11.7.5.86=0
libdeflate=1.18=h0b41bf4_0
libedit=3.1.20191231=he28a2e2_2
libev=4.33=h516909a_1
libexpat=2.5.0=hcb278e6_1
libffi=3.4.4=h6a678d5_0
libgcc-ng=13.1.0=he5830b7_0
libgfortran-ng=13.1.0=h69a702a_0
libgfortran5=13.1.0=h15d22d2_0
libglib=2.76.4=hebfc3b9_0
libgrpc=1.54.2=hb20ce57_2
libiconv=1.17=h166bdaf_0
libidn2=2.3.4=h5eee18b_0
liblapack=3.9.0=16_linux64_mkl
libllvm14=14.0.6=hcd5def8_3
libnghttp2=1.52.0=h61bc06f_0
libnpp=11.8.0.86=0
libnvjpeg=11.9.0.86=0
libpng=1.6.39=h753d276_0
libprotobuf=3.21.12=h3eb15da_0
libsodium=1.0.18=h7b6447c_0
libssh2=1.11.0=h0841786_0
libstdcxx-ng=13.1.0=hfd8a6a1_0
libtasn1=4.19.0=h5eee18b_0
libtiff=4.2.0=hecacb30_2
libunistring=0.9.10=h27cfd23_0
libwebp-base=1.3.1=hd590300_0
libxcb=1.15=h0b41bf4_0
libzlib=1.2.13=hd590300_5
lightning=2.0.4=pyhd8ed1ab_0
lightning-cloud=0.5.37=pyhd8ed1ab_0
lightning-utilities=0.9.0=pyhd8ed1ab_0
llvm-openmp=16.0.6=h4dfa4b3_0
llvmlite=0.40.1=py39h174d805_0
lockfile=0.12.2=py_1
markdown-it-py=3.0.0=pyhd8ed1ab_0
markupsafe=2.1.1=py39h7f8727e_0
matplotlib=3.8.2=pypi_0
matplotlib-inline=0.1.6=py39h06a4308_0
mdurl=0.1.0=pyhd8ed1ab_0
mkl=2022.2.1=h84fe81f_16997
ml-collections=0.1.1=pyhd8ed1ab_0
ml_dtypes=0.2.0=py39h40cae4c_1
more-itertools=9.1.0=pyhd8ed1ab_0
mpc=1.1.0=h10f8cd9_1
mpfr=4.0.2=hb69a4c5_1
mpmath=1.2.1=py39h06a4308_0
msgpack-python=1.0.5=py39h4b4f3f3_0
mudata=0.2.3=pyhd8ed1ab_0
multipledispatch=0.6.0=py_0
munch=4.0.0=pypi_0
munkres=1.1.4=pyh9f0ad1d_0
natsort=8.4.0=pyhd8ed1ab_0
ncurses=6.4=h6a678d5_0
nest-asyncio=1.5.6=py39h06a4308_0
nettle=3.7.3=hbbd107a_1
networkx=2.8.4=py39h06a4308_1
ninja=1.10.2=h06a4308_5
ninja-base=1.10.2=hd09550d_5
numba=0.57.1=py39hb75a051_0
numpy=1.24.4=py39h6183b62_0
numpyro=0.12.1=pyhd8ed1ab_0
olefile=0.46=pyhd3eb1b0_0
opencv-python=********=pypi_0
opencv-python-headless=********=pypi_0
openh264=2.1.1=h4ff587b_0
openpyxl=3.1.2=py39hd1e30aa_0
openssl=3.1.1=hd590300_1
opt_einsum=3.3.0=pyhd8ed1ab_1
optax=0.1.5=pyhd8ed1ab_0
ordered-set=4.1.0=pyhd8ed1ab_0
orjson=3.9.2=py39h10b2342_0
packaging=23.1=pyhd8ed1ab_0
pandas=2.1.4=pypi_0
parso=0.8.3=pyhd3eb1b0_0
pathtools=0.1.2=pypi_0
patsy=0.5.3=pyhd8ed1ab_0
pcre2=10.40=hc3806b6_0
pexpect=4.8.0=pyh1a96a4e_2
pickleshare=0.7.5=pyhd3eb1b0_1003
pillow=10.2.0=pypi_0
pip=23.1.2=py39h06a4308_0
pkginfo=1.9.6=pyhd8ed1ab_0
pkgutil-resolve-name=1.3.10=pyhd8ed1ab_0
platformdirs=2.6.2=pyhd8ed1ab_0
poetry=1.4.0=py39h06a4308_0
poetry-core=1.5.1=py39h06a4308_0
poetry-plugin-export=1.3.0=py39h4849bfd_0
pooch=1.7.0=pyha770c72_3
pretrainedmodels=0.7.4=pypi_0
prompt-toolkit=3.0.36=py39h06a4308_0
protobuf=4.24.1=pypi_0
psutil=5.9.5=py39h72bdee0_0
pthread-stubs=0.4=h36c2ea0_1001
ptyprocess=0.7.0=pyhd3deb0d_0
pure_eval=0.2.2=pyhd3eb1b0_0
pycparser=2.21=pyhd3eb1b0_0
pydantic=2.5.3=pypi_0
pydantic-core=2.14.6=pypi_0
pygments=2.15.1=pyhd8ed1ab_0
pyjwt=2.7.0=pyhd8ed1ab_0
pynndescent=0.5.10=pyh1a96a4e_0
pyopenssl=23.2.0=pyhd8ed1ab_1
pyparsing=3.1.0=pyhd8ed1ab_0
pyproject_hooks=1.0.0=pyhd8ed1ab_0
pyreadr=0.4.9=pypi_0
pyro-api=0.1.2=pyhd8ed1ab_0
pyro-ppl=1.8.4=pyhd8ed1ab_0
pysocks=1.7.1=pyha2e5f31_6
python=3.9.17=h955ad1f_0
python-build=0.10.0=pyhd8ed1ab_1
python-dateutil=2.8.2=pyhd8ed1ab_0
python-editor=1.0.4=py_0
python-graphviz=0.20.1=pypi_0
python-installer=0.6.0=py39h06a4308_0
python-multipart=0.0.6=pyhd8ed1ab_0
python-tzdata=2023.3=pyhd8ed1ab_0
python_abi=3.9=2_cp39
pytorch=2.0.1=py3.9_cuda11.8_cudnn8.7.0_0
pytorch-cuda=11.8=h7e8668a_5
pytorch-lightning=2.0.4=pyhd8ed1ab_0
pytorch-mutex=1.0=cuda
pytz=2023.3=pyhd8ed1ab_0
pyyaml=6.0.1=pypi_0
pyzmq=25.1.0=py39h6a678d5_0
rapidfuzz=2.15.1=py39h227be39_0
re2=2023.03.02=h8c504da_0
readchar=4.0.5=pyhd8ed1ab_0
readline=8.2=h5eee18b_0
referencing=0.29.1=pyhd8ed1ab_0
requests=2.31.0=pyhd8ed1ab_0
requests-toolbelt=0.10.1=pyhd8ed1ab_0
rich=13.4.2=pyhd8ed1ab_0
roifile=2024.1.10=pypi_0
rpds-py=0.8.10=py39h9fdd4d6_0
safetensors=0.4.1=pypi_0
scanpy=1.9.3=pyhd8ed1ab_0
scikit-image=0.22.0=pypi_0
scikit-learn=1.3.0=py39hc236052_0
scipy=1.11.4=pypi_0
scvi-tools=1.0.2=pyhd8ed1ab_0
seaborn=0.12.2=hd8ed1ab_0
seaborn-base=0.12.2=pyhd8ed1ab_0
secretstorage=3.3.3=py39hf3d152e_1
segmentation-models-pytorch=0.3.3=pypi_0
sentry-sdk=1.29.2=pypi_0
session-info=1.0.0=pyhd8ed1ab_0
setproctitle=1.3.2=pypi_0
setuptools=67.8.0=py39h06a4308_0
shapely=2.0.2=pypi_0
shellingham=1.5.1=pyhd8ed1ab_0
six=1.16.0=pyh6c4a22f_0
sleef=3.5.1=h9b69904_2
smmap=5.0.0=pypi_0
sniffio=1.3.0=pyhd8ed1ab_0
soupsieve=2.3.2.post1=pyhd8ed1ab_0
sparse=0.14.0=pyhd8ed1ab_0
sqlite=3.41.2=h5eee18b_0
stack_data=0.2.0=pyhd3eb1b0_0
starlette=0.27.0=pyhd8ed1ab_0
starsessions=1.3.0=pyhd8ed1ab_0
statsmodels=0.14.0=py39h0f8d45d_1
stdlib-list=0.8.0=pyhd8ed1ab_0
sympy=1.11.1=py39h06a4308_0
tbb=2021.8.0=hdb19cb5_0
threadpoolctl=3.1.0=pyh8a188c0_0
tifffile=2023.12.9=pypi_0
timm=0.9.2=pypi_0
tk=8.6.12=h1ccaba5_0
tomli=2.0.1=pyhd8ed1ab_0
tomlkit=0.11.8=pyha770c72_0
toolz=0.12.0=pyhd8ed1ab_0
torchaudio=2.0.2=py39_cu118
torchmetrics=0.11.4=pyhd8ed1ab_0
torchtriton=2.0.0=py39
torchvision=0.15.2=py39_cu118
torchviz=0.0.2=pypi_0
tornado=6.2=py39h5eee18b_0
tqdm=4.66.1=pypi_0
traitlets=5.9.0=pyhd8ed1ab_0
trove-classifiers=2023.7.6=pyhd8ed1ab_0
typing-extensions=4.6.3=py39h06a4308_0
typing_extensions=4.6.3=py39h06a4308_0
tzdata=2023c=h04d1e81_0
umap-learn=0.5.3=py39hf3d152e_1
unicodedata2=15.0.0=py39hb9d737c_0
urllib3=1.26.15=pyhd8ed1ab_0
uvicorn=0.22.0=py39hf3d152e_0
virtualenv=20.17.1=py39hf3d152e_0
wandb=0.15.8=pypi_0
wcwidth=0.2.6=pyhd8ed1ab_0
webencodings=0.5.1=py_1
websocket-client=1.6.1=pyhd8ed1ab_0
websockets=11.0.3=py39hd1e30aa_0
wheel=0.38.4=py39h06a4308_0
xarray=2023.6.0=pyhd8ed1ab_0
xlrd=1.2.0=pyh9f0ad1d_1
xorg-libxau=1.0.11=hd590300_0
xorg-libxdmcp=1.1.3=h7f98852_0
xz=5.4.2=h5eee18b_0
yaml=0.2.5=h7f98852_2
zeromq=4.3.4=h2531618_0
zipp=3.16.0=pyhd8ed1ab_1
zlib=1.2.13=hd590300_5
zstd=1.5.2=hfc55251_7
