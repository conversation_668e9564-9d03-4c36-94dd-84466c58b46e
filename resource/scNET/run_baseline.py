#!/usr/bin/env python3
"""
Minimal scNET Baseline Runner
=============================
This script provides a simple way to run original scNET without import issues.
"""

import sys
import os
import warnings
from pathlib import Path

# Change to scNET directory
script_dir = Path(__file__).parent
os.chdir(script_dir)

# Add current directory to path
if str(script_dir) not in sys.path:
    sys.path.insert(0, str(script_dir))

# Suppress warnings
warnings.filterwarnings('ignore')

# Create a temporary fixed version of main.py
def create_fixed_main():
    """Create a version of main.py with fixed imports."""
    with open('main.py', 'r') as f:
        content = f.read()
    
    # Fix the problematic import
    content = content.replace('import scNET.Utils as ut', 'import Utils as ut')
    content = content.replace('from scNET.MultyGraphModel import scNET', 'from MultyGraphModel import scNET')
    content = content.replace('from scNET.Utils import save_model, save_obj', 'from Utils import save_model, save_obj')
    content = content.replace('from scNET.KNNDataset import KNNDataset, CellDataset', 'from KNNDataset import KNNDataset, CellDataset')
    
    with open('main_fixed.py', 'w') as f:
        f.write(content)

# Create fixed version
create_fixed_main()

try:
    # Import from the fixed version
    from main import run_scNET
    from Utils import load_embeddings, save_obj, load_obj
    from coEmbeddedNetwork import build_co_embeded_network, create_reconstructed_obj
    
    print("✅ scNET modules imported successfully")
    
    def run_scnet_baseline(adata, model_name="baseline_comparison", **kwargs):
        """
        Run original scNET for baseline comparison.
        
        Args:
            adata: AnnData object with preprocessed single-cell data
            model_name: Name for saving model outputs
            **kwargs: Additional parameters for run_scNET
            
        Returns:
            dict: Results including embeddings and reconstructed data
        """
        # Set default parameters for comparison
        default_params = {
            'pre_processing_flag': False,  # Data already preprocessed
            'human_flag': False,
            'number_of_batches': 5,
            'split_cells': True,
            'max_epoch': 50,
            'model_name': model_name,
            'save_model_flag': True,
            'n_neighbors': 15
        }
        
        # Update with provided kwargs
        default_params.update(kwargs)
        
        print(f"🚀 Running scNET with parameters: {default_params}")
        
        # Run scNET
        model = run_scNET(adata.copy(), **default_params)
        
        # Load results
        embedded_genes, embedded_cells, node_features, out_features = load_embeddings(model_name)
        
        # Create reconstructed object
        recon_obj = create_reconstructed_obj(node_features, out_features, adata)
        
        # Build co-embedded network
        network, modularity = build_co_embeded_network(embedded_genes, node_features)
        
        return {
            'model': model,
            'embedded_genes': embedded_genes,
            'embedded_cells': embedded_cells,
            'node_features': node_features,
            'out_features': out_features,
            'reconstructed_obj': recon_obj,
            'network': network,
            'modularity': modularity
        }
        
except Exception as e:
    print(f"❌ Failed to import scNET modules: {e}")
    print("Available files in directory:", os.listdir('.'))
    raise

if __name__ == "__main__":
    print("scNET baseline runner loaded successfully")
    print("Use run_scnet_baseline(adata) to run scNET baseline")