name: PPI
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  - alabaster=0.7.12=pyhd3eb1b0_0
  - anyio=3.5.0=py310haa95532_0
  - argon2-cffi=20.1.0=py310h2bbff1b_1
  - arrow=1.2.2=pyhd3eb1b0_0
  - astroid=2.11.7=py310haa95532_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - atomicwrites=1.4.0=py_0
  - attrs=21.4.0=pyhd3eb1b0_0
  - autopep8=1.6.0=pyhd3eb1b0_1
  - babel=2.9.1=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - bcrypt=3.2.0=py310h2bbff1b_1
  - beautifulsoup4=4.11.1=py310haa95532_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - black=22.6.0=py310haa95532_0
  - blas=2.116=mkl
  - blas-devel=3.9.0=16_win64_mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - brotlipy=0.7.0=py310he2412df_1004
  - bzip2=1.0.8=he774522_0
  - ca-certificates=2023.01.10=haa95532_0
  - certifi=2022.9.14=py310haa95532_0
  - cffi=1.15.1=py310hcbf9ad4_0
  - chardet=4.0.0=py310haa95532_1003
  - charset-normalizer=2.1.1=pyhd8ed1ab_0
  - cloudpickle=2.0.0=pyhd3eb1b0_0
  - colorama=0.4.5=py310haa95532_0
  - cookiecutter=1.7.3=pyhd3eb1b0_0
  - cryptography=37.0.4=py310ha857299_0
  - cudatoolkit=11.6.0=hc0ea762_10
  - debugpy=1.5.1=py310hd77b12b_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.4=pyhd3eb1b0_0
  - docutils=0.18.1=py310haa95532_3
  - entrypoints=0.4=py310haa95532_0
  - executing=0.8.3=pyhd3eb1b0_0
  - flake8=4.0.1=pyhd3eb1b0_1
  - freetype=2.10.4=h546665d_1
  - icu=58.2=ha925a31_3
  - idna=3.4=pyhd8ed1ab_0
  - imagesize=1.4.1=py310haa95532_0
  - importlib-metadata=4.11.3=py310haa95532_0
  - importlib_metadata=4.11.3=hd3eb1b0_0
  - inflection=0.5.1=py310haa95532_0
  - intel-openmp=2022.1.0=h57928b3_3787
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.15.2=py310haa95532_0
  - ipython=7.31.1=py310haa95532_1
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - isort=5.9.3=pyhd3eb1b0_0
  - jedi=0.18.1=py310haa95532_1
  - jellyfish=0.9.0=py310h2bbff1b_0
  - jinja2-time=0.2.0=pyhd3eb1b0_3
  - jpeg=9e=h8ffe710_2
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.4.0=py310haa95532_0
  - jupyter_client=7.3.5=py310haa95532_0
  - jupyter_core=4.10.0=py310haa95532_0
  - jupyter_server=1.18.1=py310haa95532_0
  - jupyterlab=3.4.4=py310haa95532_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.12.0=py310haa95532_0
  - keyring=23.4.0=py310haa95532_0
  - lazy-object-proxy=1.6.0=py310h2bbff1b_0
  - libblas=3.9.0=16_win64_mkl
  - libcblas=3.9.0=16_win64_mkl
  - libffi=3.4.2=hd77b12b_4
  - liblapack=3.9.0=16_win64_mkl
  - liblapacke=3.9.0=16_win64_mkl
  - libpng=1.6.37=h1d00b33_2
  - libsodium=1.0.18=h62dcd97_0
  - libspatialindex=1.9.3=h6c2663c_0
  - libtiff=4.2.0=h0c97f57_3
  - libuv=1.44.2=h8ffe710_0
  - libwebp=1.2.4=h8ffe710_0
  - libwebp-base=1.2.4=h8ffe710_0
  - lz4-c=1.9.3=h8ffe710_1
  - m2w64-gcc-libgfortran=5.3.0=6
  - m2w64-gcc-libs=5.3.0=7
  - m2w64-gcc-libs-core=5.3.0=7
  - m2w64-gmp=6.1.0=2
  - m2w64-libwinpthread-git=5.0.0.4634.697f757=2
  - markupsafe=2.1.1=py310h2bbff1b_0
  - matplotlib-inline=0.1.6=py310haa95532_0
  - mccabe=0.6.1=py310haa95532_2
  - mistune=0.8.4=py310h2bbff1b_1000
  - mkl=2022.1.0=h6a75c08_874
  - mkl-devel=2022.1.0=h57928b3_875
  - mkl-include=2022.1.0=h6a75c08_874
  - msys2-conda-epoch=20160418=1
  - mypy_extensions=0.4.3=py310haa95532_1
  - nbclassic=0.3.5=pyhd3eb1b0_0
  - nbclient=0.5.13=py310haa95532_0
  - nbconvert=6.4.4=py310haa95532_0
  - nbformat=5.5.0=py310haa95532_0
  - nest-asyncio=1.5.5=py310haa95532_0
  - notebook=6.4.12=py310haa95532_0
  - numpy=1.23.2=py310h8a5b91a_0
  - numpydoc=1.4.0=py310haa95532_0
  - openssl=1.1.1t=h2bbff1b_0
  - packaging=21.3=pyhd3eb1b0_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - paramiko=2.8.1=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pathspec=0.9.0=py310haa95532_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.2.0=py310hdc2b20a_1
  - pip=22.1.2=py310haa95532_0
  - platformdirs=2.5.2=py310haa95532_0
  - pluggy=1.0.0=py310haa95532_1
  - poyo=0.5.0=pyhd3eb1b0_0
  - prometheus_client=0.14.1=py310haa95532_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - psutil=5.9.0=py310h2bbff1b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycodestyle=2.8.0=pyhd3eb1b0_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pydocstyle=6.1.1=pyhd3eb1b0_0
  - pyflakes=2.4.0=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pylint=2.14.5=py310haa95532_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pynacl=1.5.0=py310h8cc25b3_0
  - pyopenssl=22.0.0=pyhd8ed1ab_0
  - pyqt=5.9.2=py310hd77b12b_6
  - pyrsistent=0.18.0=py310h2bbff1b_0
  - pysocks=1.7.1=pyh0701188_6
  - python=3.10.4=hbb2ffb3_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py310haa95532_0
  - python-lsp-black=1.0.0=pyhd3eb1b0_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.3.3=pyhd3eb1b0_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python_abi=3.10=2_cp310
  - pytorch=1.12.1=py3.10_cuda11.6_cudnn8_0
  - pytorch-mutex=1.0=cuda
  - pywin32=302=py310h2bbff1b_2
  - pywin32-ctypes=0.2.0=py310haa95532_1000
  - pywinpty=2.0.2=py310h5da7b33_0
  - pyyaml=6.0=py310h2bbff1b_0
  - pyzmq=23.2.0=py310hd77b12b_0
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.1.10=pyhd3eb1b0_0
  - qt=5.9.7=vc14h73c81de_0
  - qtawesome=1.0.3=pyhd3eb1b0_0
  - qtconsole=5.2.2=pyhd3eb1b0_0
  - qtpy=2.2.0=py310haa95532_0
  - requests=2.28.1=pyhd8ed1ab_1
  - rope=0.22.0=pyhd3eb1b0_0
  - rtree=0.9.7=py310h2eaa2aa_1
  - send2trash=1.8.0=pyhd3eb1b0_1
  - sip=4.19.13=py310hd77b12b_0
  - six=1.16.0=pyhd3eb1b0_1
  - sniffio=1.2.0=py310haa95532_1
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.3.1=pyhd3eb1b0_0
  - sphinx=5.0.2=py310haa95532_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - spyder=5.2.2=py310haa95532_1
  - sqlite=3.39.2=h2bbff1b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - tbb=2021.5.0=h2d74725_1
  - terminado=0.13.1=py310haa95532_0
  - testpath=0.6.0=py310haa95532_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tinycss=0.4=pyhd3eb1b0_1002
  - tk=8.6.12=h2bbff1b_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=py310haa95532_0
  - tomlkit=0.11.1=py310haa95532_0
  - torchaudio=0.12.1=py310_cu116
  - torchvision=0.13.1=py310_cu116
  - tornado=6.2=py310h2bbff1b_0
  - traitlets=5.1.1=pyhd3eb1b0_0
  - typing_extensions=4.3.0=pyha770c72_0
  - ujson=5.4.0=py310hd77b12b_0
  - unidecode=1.2.0=pyhd3eb1b0_0
  - urllib3=1.26.11=pyhd8ed1ab_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - watchdog=2.1.6=py310haa95532_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py310haa95532_1
  - websocket-client=0.58.0=py310haa95532_4
  - wheel=0.37.1=pyhd3eb1b0_0
  - win_inet_pton=1.1.0=py310h5588dad_4
  - wincertstore=0.2=py310haa95532_2
  - winpty=0.4.3=4
  - wrapt=1.14.1=py310h2bbff1b_0
  - xz=5.2.5=h8cc25b3_1
  - yaml=0.2.5=he774522_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - zeromq=4.3.4=hd77b12b_0
  - zipp=3.8.0=py310haa95532_0
  - zlib=1.2.12=h8cc25b3_3
  - zstd=1.5.0=h6255e5f_0
  - pip:
    - absl-py==1.2.0
    - anndata==0.8.0
    - antlr4-python3-runtime==4.9.3
    - astunparse==1.6.3
    - bidict==0.22.0
    - biomart==0.9.2
    - biopython==1.80
    - biothings-client==0.2.6
    - bokeh==2.4.3
    - cachetools==5.2.0
    - cfgraph==0.2.1
    - click==8.1.3
    - colorcet==3.0.1
    - commonmark==0.9.1
    - contourpy==1.0.5
    - cscore==0.0.1
    - cycler==0.11.0
    - dacite==1.7.0
    - dask==2022.12.1
    - dataclasses==0.6
    - datashader==0.14.3
    - datashape==0.5.2
    - diskcache==5.4.0
    - docopt==0.6.2
    - exceptiongroup==1.1.0
    - flatbuffers==2.0.7
    - fonttools==4.37.3
    - fsspec==2022.11.0
    - gast==0.4.0
    - gensim==4.3.2
    - goatools==1.2.4
    - google-auth==2.11.1
    - google-auth-oauthlib==0.4.6
    - google-pasta==0.2.0
    - grpcio==1.48.1
    - gsea-api==0.3.4
    - gseapy==1.0.4
    - gurobipy==10.0.3
    - h5py==3.7.0
    - holoviews==1.15.3
    - igraph==0.10.1
    - imageio==2.23.0
    - iniconfig==1.1.1
    - ipympl==0.9.2
    - ipywidgets==8.0.4
    - isodate==0.6.1
    - jinja2==3.1.2
    - joblib==1.2.0
    - jsobject==0.10.2
    - jsonasobj==1.3.1
    - jsonpath-rw==1.4.0
    - jsonpickle==3.0.1
    - jupyter-helpers==0.2.3
    - jupyterlab-widgets==3.0.5
    - keras==2.10.0
    - keras-preprocessing==1.1.2
    - kiwisolver==1.4.4
    - lazy-loader==0.1rc2
    - leiden-clustering==0.1.0
    - leidenalg==0.9.0
    - libclang==14.0.6
    - littleutils==0.2.2
    - llvmlite==0.39.1
    - locket==1.0.0
    - markdown==3.4.1
    - marshmallow==3.19.0
    - matplotlib==3.6.0
    - mpmath==1.3.0
    - multipledispatch==0.6.0
    - mygene==3.2.2
    - natsort==8.2.0
    - netnmfsc==2.0
    - networkx==2.8.6
    - numba==0.56.2
    - oauthlib==3.2.1
    - ontobio==2.8.3
    - opt-einsum==3.3.0
    - outdated==0.2.1
    - pandas==1.5.0
    - pandas-flavor==0.3.0
    - panel==0.14.2
    - param==1.12.3
    - partd==1.3.0
    - patsy==0.5.2
    - pingouin==0.5.2
    - pkginfo==1.9.2
    - plotly==5.11.0
    - ply==3.11
    - powerlaw==1.5
    - prefixcommons==0.1.12
    - protobuf==3.19.5
    - pulp==2.7.0
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pyct==0.4.8
    - pydot==1.4.2
    - pydotplus==2.0.2
    - pyjsg==0.11.10
    - pynndescent==0.5.7
    - pyparsing==2.4.7
    - pyshex==0.8.1
    - pyshexc==0.9.1
    - pysolr==3.9.0
    - pytest==7.2.0
    - pytest-logging==2015.11.4
    - python-louvain==0.16
    - pytz==2022.2.1
    - pytz-deprecation-shim==0.1.0.post0
    - pyviz-comms==2.2.1
    - pywavelets==1.4.1
    - rdflib==6.2.0
    - rdflib-jsonld==0.6.1
    - rdflib-shim==1.0.3
    - readme-renderer==37.3
    - regex==2022.10.31
    - requests-oauthlib==1.3.1
    - requests-toolbelt==0.10.1
    - rfc3986==2.0.0
    - rich==12.6.0
    - rpy2==3.5.4
    - rsa==4.9
    - scanpy==1.9.1
    - scikit-image==0.19.3
    - scikit-learn==1.0.2
    - scipy==1.9.1
    - seaborn==0.12.0
    - session-info==1.0.0
    - setuptools==59.8.0
    - shexjsg==0.8.2
    - smart-open==6.4.0
    - sparqlslurper==0.5.1
    - sparqlwrapper==2.0.0
    - spyder-kernels==2.3.3
    - statsmodels==0.13.2
    - stdlib-list==0.8.0
    - tabulate==0.8.10
    - tenacity==8.1.0
    - tensorboard==2.10.0
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - tensorflow==2.10.0
    - tensorflow-estimator==2.10.0
    - tensorflow-io-gcs-filesystem==0.27.0
    - termcolor==2.0.1
    - texttable==1.6.4
    - threadpoolctl==3.1.0
    - tifffile==2022.10.10
    - toolz==0.12.0
    - torch-cluster==1.6.0
    - torch-geometric==2.1.0.post1
    - torch-scatter==2.0.9
    - torch-sparse==0.6.15
    - torch-spline-conv==1.2.1
    - torchmetrics==0.9.3
    - torchtext==0.13.1
    - tqdm==4.64.1
    - twine==4.0.2
    - tzdata==2022.4
    - tzlocal==4.2
    - umap-learn==0.5.3
    - werkzeug==2.2.2
    - widgetsnbextension==4.0.5
    - xarray==2022.9.0
    - xlrd==1.2.0
    - xlsxwriter==3.0.6
    - yamldown==0.1.8
