#!/usr/bin/env python3
"""
Fix all scNET import issues by creating corrected versions of all files
"""

import os
import shutil
from pathlib import Path

def fix_file_imports(filepath, backup=True):
    """Fix imports in a single file."""
    if backup:
        shutil.copy2(filepath, filepath + '.original')
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix all scNET package imports
    replacements = [
        ('from scNET.MultyGraphModel import scNET', 'from MultyGraphModel import scNET'),
        ('from scNET.Utils import save_model, save_obj', 'from Utils import save_model, save_obj'),
        ('from scNET.KNNDataset import KNNDataset, CellDataset', 'from KNNDataset import KNNDataset, CellDataset'),
        ('import scNET.Utils as ut', 'import Utils as ut'),
        ('import scNET.main as main', 'import main_fixed as main'),
        ('from scNET.main import run_scNET', 'from main_fixed import run_scNET'),
        ('import scNET.Utils as Utils', 'import Utils'),
        ('from scNET.coEmbeddedNetwork import', 'from coEmbeddedNetwork import'),
        ('import scNET', '# import scNET  # Fixed'),
    ]
    
    modified = False
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            modified = True
            print(f"  Fixed: {old} -> {new}")
    
    if modified:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Fixed {filepath}")
    else:
        print(f"⚪ No changes needed for {filepath}")
    
    return modified

def main():
    """Fix all import issues in scNET files."""
    files_to_fix = [
        'main.py',
        'Utils.py', 
        'MultyGraphModel.py',
        'coEmbeddedNetwork.py',
        'KNNDataset.py'
    ]
    
    print("🔧 Fixing scNET import issues...")
    
    for filename in files_to_fix:
        if os.path.exists(filename):
            print(f"\n📁 Processing {filename}:")
            fix_file_imports(filename)
        else:
            print(f"❌ File not found: {filename}")
    
    print("\n✅ All import fixes applied!")
    print("🔄 You can restore original files using the .original backups")

if __name__ == "__main__":
    main()