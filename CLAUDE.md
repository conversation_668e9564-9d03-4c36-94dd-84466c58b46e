# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains two advanced bioinformatics machine learning models for single-cell classification:

1. **scClassify2**: A message passing neural network framework for adjacent cell state identification using dual layer architecture and ordinal regression
2. **scNET**: A graph neural network model with dual-graph architecture for single-cell analysis using gene co-expression networks

## Deep Architectural Analysis

### scNET - Dual-Graph Architecture

scNET implements a sophisticated **dual-graph architecture** that simultaneously processes two distinct graph structures:

#### Graph Components
1. **Cell-Cell k-NN Graph** (`knn_edge_index`):
   - Built from single-cell expression data using k-nearest neighbors
   - Represents cell-cell similarities in expression space
   - Processed with SAGEConv layers in column dimension

2. **Gene-Gene PPI Graph** (`ppi_edge_index`): 
   - Constructed from protein-protein interaction networks (H_sapiens.net)
   - Represents biological gene-gene relationships
   - Processed with SAGEConv/GCNConv layers in row dimension

#### Key Architectural Components

- **MutualEncoder** (`MultyGraphModel.py:35-64`):
  - Alternates between cell and gene graph processing
  - Implements cross-talk between two graph domains
  - Uses `num_layers` of SAGEConv for both dimensions
  - Information flow: cells → genes → cells → genes...

- **DimEncoder** (`MultyGraphModel.py:101-151`):
  - Separate encoders for rows (genes) and columns (cells)
  - Features TransformerConvReducrLayer with attention-based edge pruning
  - Dynamic graph reduction mechanism via `reduce_network()` method
  - Attention weight tracking for graph sparsification

- **FeatureDecoder** (`MultyGraphModel.py:16-33`):
  - 3-layer MLP for reconstructing cell features
  - Maps from embedding dimension back to original feature space

#### Training Strategy
- Mini-batch processing by edges or cells (controlled by `cell_flag`)
- Graph edge splitting for train/test evaluation
- Multi-task loss combining edge reconstruction and feature recovery

### scClassify2 - Message Passing Neural Network

scClassify2 uses a sophisticated message passing architecture with ordinal regression for cell state classification:

#### Key Architectural Components

- **Feature Construction** (`utils.py:Features`):
  - Creates gene-gene edges based on expression ratios
  - Samples k positive and k negative neighbors
  - Uses log-ratio transformations for stability

- **Encoder Layers** (`model.py:EncLayer`):
  - Processes node (gene) and edge features jointly
  - Message passing: aggregates information from neighboring genes
  - Dual pathway with LayerNorm and GELU activations
  - Information mixing module for feature refinement

- **Decoder Layers** (`model.py:DecLayer`):
  - Recovers gene embeddings from encoded representations
  - Used for reconstruction task

- **Cell State Prediction** (`model.py:126-172`):
  - Three-stage transformation: W_cell1 → W_cell2 → W_cell3
  - Processes edge-aggregated features
  - Outputs logits for ordinal regression

#### Innovation: Ordinal Regression
- **CORN Loss** (`loss_function.py:CornLoss`):
  - Treats cell states as ordered categories
  - Creates binary classification tasks for each threshold
  - Enables identification of adjacent cell states

- **Sinkhorn Distance** (`loss_function.py:SinkhornDistance`):
  - Optimal transport distance for distribution matching
  - Used for gene expression recovery task
  - Balances reconstruction with classification

## Technical Implementation Details

### scNET Technical Details

```python
# Model initialization
scNET(col_dim, row_dim, inter_row_dim, embd_row_dim, 
      inter_col_dim, embd_col_dim, lambda_rows, lambda_cols)

# Key parameters
INTER_DIM = 250        # Intermediate dimension
EMBEDDING_DIM = 75     # Final embedding dimension  
NETWORK_CUTOFF = 0.5   # PPI network edge threshold
NUM_LAYERS = 3         # Number of mutual encoding layers
```

### scClassify2 Technical Details

```python
# Model initialization
scMPNN(num_states, num_genes, gene_vecdim=200, 
       node_feature_dim=128, edge_feature_dim=64,
       hidden_dim=128, num_encoder_layers=2, 
       num_decoder_layers=1, k_neighbors_pos=8, 
       k_neighbors_neg=8)

# Training parameters
lr = 1e-4
batch_size = 32 (default)
n_epoch = 28
scheduler: StepLR with gamma=0.5, step_size=10
```

## Development Environment Setup

### scClassify2 Environment
```bash
conda create -n scclassify2 python=3.9
conda activate scclassify2
conda install -c conda-forge anndata scanpy
conda install pytorch torchvision torchaudio pytorch-cuda -c pytorch -c nvidia
pip install wandb torchviz scikit-misc

# h5py reinstallation if needed
pip uninstall h5py && pip install h5py
```

### scNET Environment
```bash
# For Linux/Unix systems
conda create -n scnet python=3.10
conda activate scnet
conda install pytorch pytorch-cuda=11.8 -c pytorch -c nvidia
conda install pyg -c pyg
pip install scanpy anndata networkx

# Or use provided environment file (Windows-based)
conda env create -f resource/scNET/Data/scNET-env.yaml
```

## Running the Models

### scClassify2 Training
```bash
# Demo data
python paper/scClassify2/main.py \
    --trainset_path ./paper/scClassify2/train_set.h5ad \
    --label_path ./paper/scClassify2/label.json \
    --batch_size 32

# Custom data (requires H5AD with "CellState" column)
python paper/scClassify2/main.py \
    --trainset_path ./input.h5ad \
    --label_path ./input.json
```

### scNET Training
```bash
cd resource/scNET
python main.py  # Uses pbmc3k.h5ad and H_sapiens.net
```

## Data Format Requirements

### scClassify2
- **Training data**: H5AD format with expression matrix
- **Cell labels**: "CellState" column in AnnData.obs
- **Label file**: JSON list of cell state names
- **Gene embeddings**: gene2vec_dim_200_iter_9.txt

### scNET  
- **Expression data**: H5AD format (e.g., pbmc3k.h5ad)
- **Network file**: Tab-delimited (Source, Target, Connection_Score)
- **Preprocessing**: Automatic normalization and k-NN computation

## Key Differences Between Models

| Aspect | scClassify2 | scNET |
|--------|------------|-------|
| Graph Type | Single gene-gene graph | Dual graph (cell + gene) |
| Edge Construction | Expression ratio-based | k-NN + PPI network |
| Classification | Ordinal regression | Embedding-based |
| Loss Function | CORN + Sinkhorn | Reconstruction + MSE |
| Architecture | Message passing | Mutual encoding |
| Cross-platform | Yes (log-ratio) | Dataset-specific |

## Model Outputs

### scClassify2
- Cell state predictions (ordinal)
- Gene embedding recovery
- Training logs in 'log' file
- Cross-validation results

### scNET
- Gene embeddings (row dimension)
- Cell embeddings (column dimension)  
- Reconstructed features
- Graph attention weights
- Model checkpoints in Models/

## Code Organization

### scClassify2 Components
```
paper/scClassify2/
├── main.py              # Training entry point
├── model.py             # scMPNN architecture
├── data.py              # Data loading/preprocessing
├── loss_function.py     # CORN loss, Sinkhorn distance
├── utils.py             # Feature construction, predictions
└── code_with_HOPACH/    # Hierarchical classification
```

### scNET Components
```
resource/scNET/
├── main.py              # Training script
├── MultyGraphModel.py   # Dual-graph scNET model
├── KNNDataset.py        # Graph dataset handling
├── Utils.py             # Model utilities
├── coEmbeddedNetwork.py # Co-embedding implementation
└── Data/                # Network data files
```

## Performance Considerations

- scNET uses mini-batch training for scalability (MAX_CELLS_BATCH_SIZE = 4000)
- scClassify2 implements cross-validation automatically
- Both models support GPU acceleration via CUDA
- Memory usage scales with number of genes and batch size